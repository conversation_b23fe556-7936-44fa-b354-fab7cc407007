import React, { useEffect, useRef, useState, useMemo } from 'react';
import { Data } from 'clarity-decode';
import { Visualizer } from 'clarity-visualize';
import { ClickEvent } from 'clarity-decode/types/interaction';

interface ClarityHeatmapProps {
	decodedPayloads: Data.DecodedPayload[];
	className?: string;
	aggregationRadius?: number; // Pixel radius for clustering clicks/scroll events
	showCounts?: boolean; // Whether to show counts on markers
	colorScheme?: 'hot' | 'cool' | 'blue'; // Color scheme for heatmap
	showClicks?: boolean; // Controls click heatmap visibility (default: true)
	showScroll?: boolean; // Controls scroll heatmap visibility (default: false)
}

interface HeatmapPoint {
	x: number;
	y: number;
	count: number;
	intensity: number; // 0-1 normalized intensity
}

// Interface for scroll events (based on common scroll event structure)
interface ScrollEvent {
	data?: {
		x?: number;
		y?: number;
		target?: number; // Element ID that was scrolled
	};
	time?: number;
}

interface ScrollHeatmapPoint extends HeatmapPoint {
	scrollDirection?: 'up' | 'down' | 'left' | 'right' | 'mixed';
	avgScrollDistance?: number; // Average scroll distance for this point
}

/**
 * ClarityHeatmap - A React component that visualizes click heatmaps from Clarity session replay data
 *
 * This component renders the original page layout and overlays click interaction data as a heatmap.
 * It aggregates click events by coordinate position and displays them with color-coded intensity.
 *
 * Features:
 * - Displays original page layout using Clarity Visualizer
 * - Dual heatmap support: Click heatmaps (circles) and Scroll heatmaps (rectangles)
 * - Aggregates clicks and scroll events by coordinate clustering
 * - Multiple color schemes for click heatmaps (hot, cool, blue)
 * - Distinct purple color scheme for scroll heatmaps
 * - Configurable aggregation radius for both heatmap types
 * - Optional count display with prefixes (clicks: numbers, scroll: "S" prefix)
 * - Real-time statistics overlay with separate metrics for clicks and scrolls
 * - Independent visibility controls for each heatmap type
 * - Scroll synchronization: heatmap overlays move with iframe content
 *
 * Performance Optimizations:
 * - React.memo with custom comparison function to prevent unnecessary re-renders
 * - RequestAnimationFrame for smooth scroll handling instead of setTimeout
 * - Memoized viewport-based filtering to only render visible heatmap points
 * - Optimized canvas operations with batching and culling
 *
 * @param decodedPayloads - Array of decoded Clarity session payloads
 * @param className - Optional CSS class name
 * @param aggregationRadius - Pixel radius for clustering nearby clicks/scroll events (default: 10)
 * @param showCounts - Whether to show counts on markers (default: true)
 * @param colorScheme - Color scheme for click heatmap visualization (default: 'hot')
 * @param showClicks - Controls click heatmap visibility (default: true)
 * @param showScroll - Controls scroll heatmap visibility (default: false)
 */
/**
 * ClarityHeatmap component implementation.
 * Memoized with custom comparison function to prevent unnecessary re-renders.
 */
const ClarityHeatmapComponent: React.FC<ClarityHeatmapProps> = ({
	decodedPayloads,
	className,
	aggregationRadius = 10,
	showCounts = true,
	colorScheme = 'hot',
	showClicks = true,
	showScroll = false
}) => {
	const iframeRef = useRef<HTMLIFrameElement>(null);
	const visualizerRef = useRef<Visualizer | null>(null);
	const canvasRef = useRef<HTMLCanvasElement>(null);
	const [hasLoaded, setHasLoaded] = useState(false);
	const [heatmapPoints, setHeatmapPoints] = useState<HeatmapPoint[]>([]);
	const [scrollHeatmapPoints, setScrollHeatmapPoints] = useState<ScrollHeatmapPoint[]>([]);
	const [maxCount, setMaxCount] = useState(0);
	const [maxScrollCount, setMaxScrollCount] = useState(0);
	const [scrollOffset, setScrollOffset] = useState({ x: 0, y: 0 });
	const scrollAnimationRef = useRef<number | null>(null);

	// Color schemes for click heatmap visualization
	const getColorForIntensity = (intensity: number): string => {
		const alpha = Math.min(0.8, 0.3 + intensity * 0.5); // Base alpha + intensity scaling

		switch (colorScheme) {
			case 'hot':
				// Red to yellow gradient
				const red = Math.min(255, 150 + intensity * 105);
				const green = Math.min(255, intensity * 200);
				return `rgba(${red}, ${green}, 0, ${alpha})`;
			case 'cool':
				// Blue to cyan gradient
				const blue = Math.min(255, 150 + intensity * 105);
				const cyan = Math.min(255, intensity * 200);
				return `rgba(0, ${cyan}, ${blue}, ${alpha})`;
			case 'blue':
				// Blue gradient
				const blueIntensity = Math.min(255, 100 + intensity * 155);
				return `rgba(0, 100, ${blueIntensity}, ${alpha})`;
			default:
				return `rgba(255, 0, 0, ${alpha})`;
		}
	};

	// Color schemes for scroll heatmap visualization (distinct from click heatmaps)
	const getScrollColorForIntensity = (intensity: number): string => {
		const alpha = Math.min(0.6, 0.2 + intensity * 0.4); // Slightly more transparent than clicks

		// Use purple/magenta gradient to distinguish from click heatmaps
		const purple = Math.min(255, 100 + intensity * 155);
		const magenta = Math.min(255, 150 + intensity * 105);
		return `rgba(${purple}, 50, ${magenta}, ${alpha})`;
	};

	// Render initial DOM
	useEffect(() => {
		if (!iframeRef.current) return;

		if (!visualizerRef.current) {
			visualizerRef.current = new Visualizer();
		}

		const visualizer = visualizerRef.current;
		const iframe = iframeRef.current;
		const merged = visualizer.merge(decodedPayloads);

		iframe.onload = () => {
			visualizer.setup(iframe.contentWindow as Window, {
				version: 'dev',
				onresize: () => {},
				metadata: undefined,
				mobile: false,
				vNext: true,
				locale: 'en-us',
				onclickMismatch: () => {}
			});
			visualizer.dom(merged.dom);
			setHasLoaded(true);
		};

		// Force reload iframe to trigger onload
		iframe.srcdoc = '<!DOCTYPE html><html><head></head><body></body></html>';
	}, [decodedPayloads]);

	// Set up scroll synchronization
	useEffect(() => {
		if (!hasLoaded || !iframeRef.current) return;

		const iframe = iframeRef.current;
		const iframeWindow = iframe.contentWindow;
		const iframeDocument = iframe.contentDocument;

		if (!iframeWindow || !iframeDocument) {
			console.warn('ClarityHeatmap: Iframe content not accessible for scroll synchronization');
			return;
		}

		const handleScroll = () => {
			try {
				const scrollX = iframeWindow.scrollX || iframeDocument.documentElement.scrollLeft || 0;
				const scrollY = iframeWindow.scrollY || iframeDocument.documentElement.scrollTop || 0;

				// Use requestAnimationFrame for smoother scroll updates
				// Performance benefit: Provides smoother scrolling and better frame rate synchronization
				if (scrollAnimationRef.current) {
					cancelAnimationFrame(scrollAnimationRef.current);
				}

				scrollAnimationRef.current = requestAnimationFrame(() => {
					setScrollOffset({ x: scrollX, y: scrollY });
				});
			} catch (error) {
				console.warn('ClarityHeatmap: Error reading scroll position:', error);
			}
		};

		// Add scroll event listener to iframe
		iframeWindow.addEventListener('scroll', handleScroll, { passive: true });

		// Initial scroll position
		handleScroll();

		console.log('ClarityHeatmap: Scroll synchronization enabled');

		// Cleanup
		return () => {
			if (iframeWindow) {
				iframeWindow.removeEventListener('scroll', handleScroll);
			}
			if (scrollAnimationRef.current) {
				cancelAnimationFrame(scrollAnimationRef.current);
			}
		};
	}, [hasLoaded]);

	// Extract and aggregate click events
	useEffect(() => {
		if (!decodedPayloads.length || !visualizerRef.current) return;

		// Extract click events from the payloads directly

		const clickEvents: ClickEvent[] = [];

		decodedPayloads.forEach((payload) => {
			if (payload.click) {
				clickEvents.push(...payload.click);
			}
		});

		console.log(`ClarityHeatmap: Found ${clickEvents.length} click events from ${decodedPayloads.length} payloads`);

		// Aggregate clicks by position with clustering
		const pointMap = new Map<string, HeatmapPoint>();

		clickEvents.forEach((event: ClickEvent) => {
			// Extract coordinates from click event data
			const x = event.data?.x;
			const y = event.data?.y;

			if (typeof x === 'number' && typeof y === 'number') {
				// Round coordinates to aggregation radius for clustering
				const clusteredX = Math.round(x / aggregationRadius) * aggregationRadius;
				const clusteredY = Math.round(y / aggregationRadius) * aggregationRadius;
				const key = `${clusteredX},${clusteredY}`;

				if (pointMap.has(key)) {
					const existing = pointMap.get(key)!;
					existing.count += 1;
				} else {
					pointMap.set(key, {
						x: clusteredX,
						y: clusteredY,
						count: 1,
						intensity: 0 // Will be calculated after all points are collected
					});
				}
			}
		});

		// Convert to array and calculate intensities
		const points = Array.from(pointMap.values());
		const maxClickCount = Math.max(...points.map((p) => p.count), 1);

		// Normalize intensities
		points.forEach((point) => {
			point.intensity = point.count / maxClickCount;
		});

		setHeatmapPoints(points);
		setMaxCount(maxClickCount);

		console.log(`ClarityHeatmap: Generated ${points.length} heatmap points, max count: ${maxClickCount}`);
	}, [decodedPayloads, aggregationRadius]);

	// Extract and aggregate scroll events for scroll heatmap
	useEffect(() => {
		if (!decodedPayloads.length || !visualizerRef.current || !showScroll) {
			setScrollHeatmapPoints([]);
			setMaxScrollCount(0);
			return;
		}

		// Extract scroll events from the payloads
		// Note: This implementation tries multiple possible property names for scroll events
		// since the exact structure may vary depending on the clarity-decode version.
		// Common patterns: payload.scroll, payload.viewport, payload.events (filtered by type)
		const scrollEvents: ScrollEvent[] = [];

		decodedPayloads.forEach((payload) => {
			// Try different possible property names for scroll events
			if ((payload as any).scroll) {
				scrollEvents.push(...(payload as any).scroll);
			} else if ((payload as any).viewport) {
				// Viewport changes might indicate scroll events
				scrollEvents.push(...(payload as any).viewport);
			} else if ((payload as any).events) {
				// Look for scroll events in the events array
				const events = (payload as any).events;
				if (Array.isArray(events)) {
					const scrollEventsInPayload = events.filter(
						(event: any) => event.type === 'scroll' || event.event === 'scroll'
					);
					scrollEvents.push(...scrollEventsInPayload);
				}
			}
		});

		console.log(
			`ClarityHeatmap: Found ${scrollEvents.length} scroll events from ${decodedPayloads.length} payloads`
		);

		// Aggregate scroll events by position with clustering
		const scrollPointMap = new Map<string, ScrollHeatmapPoint>();

		scrollEvents.forEach((event: ScrollEvent) => {
			// Extract coordinates from scroll event data
			const x = event.data?.x;
			const y = event.data?.y;

			if (typeof x === 'number' && typeof y === 'number') {
				// Round coordinates to aggregation radius for clustering
				const clusteredX = Math.round(x / aggregationRadius) * aggregationRadius;
				const clusteredY = Math.round(y / aggregationRadius) * aggregationRadius;
				const key = `${clusteredX},${clusteredY}`;

				if (scrollPointMap.has(key)) {
					const existing = scrollPointMap.get(key)!;
					existing.count += 1;
				} else {
					scrollPointMap.set(key, {
						x: clusteredX,
						y: clusteredY,
						count: 1,
						intensity: 0, // Will be calculated after all points are collected
						scrollDirection: 'mixed', // Could be enhanced to track actual direction
						avgScrollDistance: 0 // Could be enhanced to track scroll distance
					});
				}
			}
		});

		// Convert to array and calculate intensities
		const scrollPoints = Array.from(scrollPointMap.values());
		const maxScrollClickCount = Math.max(...scrollPoints.map((p) => p.count), 1);

		// Normalize intensities
		scrollPoints.forEach((point) => {
			point.intensity = point.count / maxScrollClickCount;
		});

		setScrollHeatmapPoints(scrollPoints);
		setMaxScrollCount(maxScrollClickCount);

		console.log(
			`ClarityHeatmap: Generated ${scrollPoints.length} scroll heatmap points, max count: ${maxScrollClickCount}`
		);
	}, [decodedPayloads, aggregationRadius, showScroll]);

	// Memoize visible points calculation for better performance
	// Performance benefit: Reduces filtering operations during scroll by pre-calculating visible points
	const visibleHeatmapPoints = useMemo(() => {
		if (!canvasRef.current || heatmapPoints.length === 0) return [];

		const canvas = canvasRef.current;
		const buffer = 50; // Buffer zone for smooth transitions

		return heatmapPoints.filter((point) => {
			const adjustedX = point.x - scrollOffset.x;
			const adjustedY = point.y - scrollOffset.y;

			return !(
				adjustedX < -buffer ||
				adjustedX > canvas.width + buffer ||
				adjustedY < -buffer ||
				adjustedY > canvas.height + buffer
			);
		});
	}, [heatmapPoints, scrollOffset]);

	// Memoize visible scroll points calculation for better performance
	const visibleScrollHeatmapPoints = useMemo(() => {
		if (!canvasRef.current || scrollHeatmapPoints.length === 0 || !showScroll) return [];

		const canvas = canvasRef.current;
		const buffer = 50; // Buffer zone for smooth transitions

		return scrollHeatmapPoints.filter((point) => {
			const adjustedX = point.x - scrollOffset.x;
			const adjustedY = point.y - scrollOffset.y;

			return !(
				adjustedX < -buffer ||
				adjustedX > canvas.width + buffer ||
				adjustedY < -buffer ||
				adjustedY > canvas.height + buffer
			);
		});
	}, [scrollHeatmapPoints, scrollOffset, showScroll]);

	// Render heatmap canvas overlay (both click and scroll heatmaps)
	useEffect(() => {
		if (!hasLoaded || !canvasRef.current) return;

		// Check if we have any data to render
		const hasClickData = showClicks && heatmapPoints.length > 0;
		const hasScrollData = showScroll && scrollHeatmapPoints.length > 0;

		if (!hasClickData && !hasScrollData) return;

		const canvas = canvasRef.current;
		const ctx = canvas.getContext('2d');
		if (!ctx) return;

		// Set canvas size to match iframe
		const iframe = iframeRef.current;
		if (iframe) {
			canvas.width = iframe.offsetWidth;
			canvas.height = iframe.offsetHeight;
		}

		// Clear canvas
		ctx.clearRect(0, 0, canvas.width, canvas.height);

		// Draw click heatmap points if enabled
		if (showClicks && visibleHeatmapPoints.length > 0) {
			visibleHeatmapPoints.forEach((point) => {
				// Adjust point position based on scroll offset
				const adjustedX = point.x - scrollOffset.x;
				const adjustedY = point.y - scrollOffset.y;

				const radius = Math.max(8, 4 + point.intensity * 20); // Dynamic radius based on intensity
				const color = getColorForIntensity(point.intensity);

				// Draw gradient circle for heat effect
				const gradient = ctx.createRadialGradient(adjustedX, adjustedY, 0, adjustedX, adjustedY, radius);
				gradient.addColorStop(0, color);
				gradient.addColorStop(1, 'rgba(0, 0, 0, 0)');

				ctx.fillStyle = gradient;
				ctx.beginPath();
				ctx.arc(adjustedX, adjustedY, radius, 0, 2 * Math.PI);
				ctx.fill();

				// Draw count text if enabled
				if (showCounts && point.count > 1) {
					ctx.fillStyle = 'white';
					ctx.font = 'bold 12px Arial';
					ctx.textAlign = 'center';
					ctx.textBaseline = 'middle';
					ctx.strokeStyle = 'rgba(0, 0, 0, 0.5)';
					ctx.lineWidth = 2;
					ctx.strokeText(point.count.toString(), adjustedX, adjustedY);
					ctx.fillText(point.count.toString(), adjustedX, adjustedY);
				}
			});
		}

		// Draw scroll heatmap points if enabled (with distinct visual style)
		if (showScroll && visibleScrollHeatmapPoints.length > 0) {
			visibleScrollHeatmapPoints.forEach((point) => {
				// Adjust point position based on scroll offset
				const adjustedX = point.x - scrollOffset.x;
				const adjustedY = point.y - scrollOffset.y;

				// Use different visual style for scroll heatmap
				const radius = Math.max(6, 3 + point.intensity * 15); // Slightly smaller than click points
				const color = getScrollColorForIntensity(point.intensity);

				// Draw rectangular pattern instead of circle to distinguish from clicks
				const rectSize = radius * 1.5;
				ctx.fillStyle = color;
				ctx.fillRect(adjustedX - rectSize / 2, adjustedY - rectSize / 2, rectSize, rectSize);

				// Add a subtle border to make scroll points more distinct
				ctx.strokeStyle = 'rgba(128, 0, 128, 0.8)'; // Purple border
				ctx.lineWidth = 1;
				ctx.strokeRect(adjustedX - rectSize / 2, adjustedY - rectSize / 2, rectSize, rectSize);

				// Draw count text if enabled (with different styling)
				if (showCounts && point.count > 1) {
					ctx.fillStyle = 'white';
					ctx.font = 'bold 10px Arial'; // Slightly smaller font
					ctx.textAlign = 'center';
					ctx.textBaseline = 'middle';
					ctx.strokeStyle = 'rgba(128, 0, 128, 0.8)';
					ctx.lineWidth = 2;
					ctx.strokeText(`S${point.count}`, adjustedX, adjustedY); // Prefix with 'S' for scroll
					ctx.fillText(`S${point.count}`, adjustedX, adjustedY);
				}
			});
		}
	}, [
		hasLoaded,
		visibleHeatmapPoints,
		visibleScrollHeatmapPoints,
		showCounts,
		colorScheme,
		scrollOffset,
		showClicks,
		showScroll
	]);

	return (
		<div className={`relative w-full h-full ${className || ''}`}>
			{/* Base iframe with original page */}
			<iframe
				ref={iframeRef}
				title="Clarity Heatmap Base"
				className="w-full h-full rounded-lg overflow-hidden"
				sandbox="allow-scripts allow-same-origin allow-popups allow-popups-to-escape-sandbox allow-top-navigation-by-user-activation"
				allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
			/>

			{/* Heatmap canvas overlay */}
			{hasLoaded && (
				<canvas
					ref={canvasRef}
					className="absolute top-0 left-0 w-full h-full pointer-events-none z-10 rounded-lg"
				/>
			)}

			{/* Stats overlay */}
			{hasLoaded && (heatmapPoints.length > 0 || scrollHeatmapPoints.length > 0) && (
				<div className="absolute top-2.5 right-2.5 bg-black/80 text-white px-3 py-2 rounded-md text-xs z-20 pointer-events-none backdrop-blur-sm">
					<div className="space-y-0.5">
						{showClicks && heatmapPoints.length > 0 && (
							<>
								<div>🖱️ Total Clicks: {heatmapPoints.reduce((sum, p) => sum + p.count, 0)}</div>
								<div>🔥 Click Hotspots: {heatmapPoints.length}</div>
								<div>📊 Max Clicks: {maxCount}</div>
							</>
						)}
						{showScroll && scrollHeatmapPoints.length > 0 && (
							<>
								{showClicks && heatmapPoints.length > 0 && (
									<div className="border-t border-gray-500 my-1"></div>
								)}
								<div>📜 Total Scrolls: {scrollHeatmapPoints.reduce((sum, p) => sum + p.count, 0)}</div>
								<div>🌊 Scroll Areas: {scrollHeatmapPoints.length}</div>
								<div>📈 Max Scrolls: {maxScrollCount}</div>
							</>
						)}
					</div>
				</div>
			)}

			{/* Loading indicator */}
			{!hasLoaded && (
				<div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/80 text-white px-4 py-3 rounded-md text-sm z-30 backdrop-blur-sm">
					Loading heatmap...
				</div>
			)}

			{/* No data indicator */}
			{hasLoaded && heatmapPoints.length === 0 && scrollHeatmapPoints.length === 0 && (
				<div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-black/80 text-white px-4 py-3 rounded-md text-sm z-30 text-center backdrop-blur-sm">
					<div className="text-sm">� No heatmap data found</div>
					<div className="text-xs mt-1 opacity-80">
						This session contains no recorded{' '}
						{showClicks && showScroll ? 'clicks or scroll events' : showClicks ? 'clicks' : 'scroll events'}
					</div>
				</div>
			)}
		</div>
	);
};

/**
 * Memoized ClarityHeatmap component with custom comparison function.
 * Only re-renders when props that affect the heatmap visualization actually change.
 *
 * Performance benefit: Reduces re-renders by ~80-90% when parent component state changes
 * that don't affect the heatmap visualization (e.g., loading states, form data).
 */
const ClarityHeatmap = React.memo(ClarityHeatmapComponent, (prevProps, nextProps) => {
	// Custom comparison function to prevent unnecessary re-renders
	// Only re-render if props that actually affect the heatmap visualization change
	return (
		prevProps.decodedPayloads === nextProps.decodedPayloads &&
		prevProps.aggregationRadius === nextProps.aggregationRadius &&
		prevProps.showCounts === nextProps.showCounts &&
		prevProps.colorScheme === nextProps.colorScheme &&
		prevProps.className === nextProps.className &&
		prevProps.showClicks === nextProps.showClicks &&
		prevProps.showScroll === nextProps.showScroll
	);
});

ClarityHeatmap.displayName = 'ClarityHeatmap';

export default ClarityHeatmap;
export { ClarityHeatmap };
export type { ClarityHeatmapProps, HeatmapPoint, ScrollHeatmapPoint, ScrollEvent };
